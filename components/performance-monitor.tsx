"use client"

import { useEffect } from "react"

interface PerformanceMetrics {
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  fcp?: number // First Contentful Paint
  ttfb?: number // Time to First Byte
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run in production and if performance API is available
    if (process.env.NODE_ENV !== "production" || typeof window === "undefined") {
      return
    }

    const metrics: PerformanceMetrics = {}

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        const lastEntry = entries[entries.length - 1] as PerformanceEntry & { renderTime?: number; loadTime?: number }
        metrics.lcp = lastEntry.renderTime || lastEntry.loadTime || 0
      }).observe({ entryTypes: ["largest-contentful-paint"] })

      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach((entry) => {
          const fidEntry = entry as PerformanceEntry & { processingStart?: number }
          metrics.fid = fidEntry.processingStart ? fidEntry.processingStart - entry.startTime : 0
        })
      }).observe({ entryTypes: ["first-input"] })

      // Cumulative Layout Shift (CLS)
      let clsValue = 0
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach((entry) => {
          const layoutShiftEntry = entry as PerformanceEntry & { value?: number; hadRecentInput?: boolean }
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value || 0
          }
        })
        metrics.cls = clsValue
      }).observe({ entryTypes: ["layout-shift"] })

      // First Contentful Paint (FCP)
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries()
        entries.forEach((entry) => {
          if (entry.name === "first-contentful-paint") {
            metrics.fcp = entry.startTime
          }
        })
      }).observe({ entryTypes: ["paint"] })
    }

    // Measure Navigation Timing
    const measureNavigationTiming = () => {
      const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming
      if (navigation) {
        metrics.ttfb = navigation.responseStart - navigation.requestStart
      }
    }

    // Send metrics to analytics (replace with your analytics service)
    const sendMetrics = () => {
      // Only send if we have meaningful data
      if (Object.keys(metrics).length > 0) {
        console.log("Performance Metrics:", metrics)
        
        // Example: Send to Google Analytics 4
        if (typeof gtag !== "undefined") {
          gtag("event", "web_vitals", {
            custom_map: {
              metric_lcp: "lcp",
              metric_fid: "fid", 
              metric_cls: "cls",
              metric_fcp: "fcp",
              metric_ttfb: "ttfb",
            },
            lcp: metrics.lcp,
            fid: metrics.fid,
            cls: metrics.cls,
            fcp: metrics.fcp,
            ttfb: metrics.ttfb,
          })
        }

        // Example: Send to custom analytics endpoint
        // fetch('/api/analytics/performance', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({
        //     url: window.location.pathname,
        //     metrics,
        //     timestamp: Date.now(),
        //     userAgent: navigator.userAgent,
        //   }),
        // }).catch(console.error)
      }
    }

    // Initialize measurements
    measureWebVitals()
    measureNavigationTiming()

    // Send metrics after page load
    const sendMetricsTimeout = setTimeout(sendMetrics, 5000) // Wait 5 seconds for metrics to stabilize

    // Cleanup
    return () => {
      clearTimeout(sendMetricsTimeout)
    }
  }, [])

  // This component doesn't render anything
  return null
}

// Utility function to get performance grade
export function getPerformanceGrade(metrics: PerformanceMetrics): {
  lcp: "good" | "needs-improvement" | "poor"
  fid: "good" | "needs-improvement" | "poor"
  cls: "good" | "needs-improvement" | "poor"
  overall: "good" | "needs-improvement" | "poor"
} {
  const lcpGrade = !metrics.lcp ? "good" : 
    metrics.lcp <= 2500 ? "good" : 
    metrics.lcp <= 4000 ? "needs-improvement" : "poor"

  const fidGrade = !metrics.fid ? "good" :
    metrics.fid <= 100 ? "good" :
    metrics.fid <= 300 ? "needs-improvement" : "poor"

  const clsGrade = !metrics.cls ? "good" :
    metrics.cls <= 0.1 ? "good" :
    metrics.cls <= 0.25 ? "needs-improvement" : "poor"

  const grades = [lcpGrade, fidGrade, clsGrade]
  const poorCount = grades.filter(g => g === "poor").length
  const needsImprovementCount = grades.filter(g => g === "needs-improvement").length

  const overall = poorCount > 0 ? "poor" :
    needsImprovementCount > 0 ? "needs-improvement" : "good"

  return {
    lcp: lcpGrade,
    fid: fidGrade,
    cls: clsGrade,
    overall,
  }
}
